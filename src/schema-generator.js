/* eslint-disable no-process-exit */
const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

const outputFile = path.join(__dirname, 'models', 'schema.js');
// Make sure this path is correct for your project structure
const { dbConfig } = require('./config/sequelize');

function pgTypeToSequelize(pgType, udtName) {
  if (udtName.startsWith('_')) {
    // This is an array type, e.g., _text, _int4
    const singularUdtName = udtName.substring(1);
    // Find the singular pgType to map to Sequelize
    const singularPgType =
      pgType === 'ARRAY' ? singularUdtName.replace(/_(\w+)/, '$1') : singularUdtName;
    const sequelizeType = pgTypeToSequelize(singularPgType, singularUdtName).split('.')[1];
    return `DataTypes.ARRAY(DataTypes.${sequelizeType})`;
  }
  const mapping = {
    'character varying': 'DataTypes.STRING',
    varchar: 'DataTypes.STRING',
    text: 'DataTypes.TEXT',
    integer: 'DataTypes.INTEGER',
    int4: 'DataTypes.INTEGER',
    bigint: 'DataTypes.BIGINT',
    int8: 'DataTypes.BIGINT',
    boolean: 'DataTypes.BOOLEAN',
    bool: 'DataTypes.BOOLEAN',
    'timestamp with time zone': 'DataTypes.DATE',
    timestamptz: 'DataTypes.DATE',
    date: 'DataTypes.DATEONLY',
    jsonb: 'DataTypes.JSONB',
    json: 'DataTypes.JSON',
    uuid: 'DataTypes.UUID',
    'double precision': 'DataTypes.DOUBLE',
    float8: 'DataTypes.DOUBLE',
    numeric: 'DataTypes.DECIMAL',
  };
  return mapping[pgType] || `DataTypes.STRING`;
}

/**
 * A more intelligent parser for PostgreSQL default values.
 * @param {string | null} defaultValue The raw default value from information_schema.
 * @param {string} udtName The PostgreSQL UDT name (e.g., 'int4', '_text', 'jsonb').
 * @returns {any} The parsed JavaScript default value.
 */
function parseDefaultValue(defaultValue, udtName) {
  if (defaultValue === null) {
    return undefined;
  }

  // Case 1: Handle array defaults
  if (udtName.startsWith('_') || defaultValue.startsWith('ARRAY[]::')) {
    return [];
  }

  // Case 2: Handle type-casted literals, e.g., 'foo'::text, '[]'::jsonb, '0'::integer
  const castMatch = defaultValue.match(/^'(.*)'::/);
  if (castMatch) {
    const value = castMatch[1];
    if (udtName === 'json' || udtName === 'jsonb') {
      try {
        return JSON.parse(value);
        // eslint-disable-next-line no-unused-vars
      } catch (e) {
        return value;
      }
    }
    if (!isNaN(parseFloat(value)) && isFinite(value)) {
      return parseFloat(value);
    }
    return value;
  }

  // Case 3: Handle simple numeric defaults
  if (!isNaN(parseFloat(defaultValue)) && isFinite(defaultValue)) {
    return parseFloat(defaultValue);
  }

  // Case 4: A naked '{}' can be an empty array default
  if (defaultValue === '{}') {
    return [];
  }

  // Fallback for other unhandled cases (e.g., functions)
  return `%%${defaultValue}%%`;
}

async function generateSchema() {
  const client = new Client({
    user: dbConfig.username,
    host: dbConfig.host,
    database: dbConfig.database,
    password: dbConfig.password,
    port: dbConfig.port,
  });

  try {
    await client.connect();
    console.log('Connected to the database to generate schema and indexes...');

    // --- Step 1: Gather all Schemas (Columns) ---
    console.log('Gathering table schemas...');
    const resSchemas = await client.query(`
      SELECT
          c.table_name,
          c.column_name,
          c.data_type,
          c.udt_name,
          c.is_nullable,
          c.column_default,
          (pk.column_name IS NOT NULL) AS is_primary_key,
          (uq.column_name IS NOT NULL) AS is_unique
      FROM
          information_schema.columns AS c
      LEFT JOIN (
          SELECT kcu.table_schema, kcu.table_name, kcu.column_name
          FROM information_schema.table_constraints AS tc
          JOIN information_schema.key_column_usage AS kcu ON tc.constraint_name = kcu.constraint_name AND tc.table_schema = kcu.table_schema
          WHERE tc.constraint_type = 'PRIMARY KEY'
      ) AS pk ON c.table_schema = pk.table_schema AND c.table_name = pk.table_name AND c.column_name = pk.column_name
      LEFT JOIN (
          SELECT ccu.table_schema, ccu.table_name, ccu.column_name
          FROM information_schema.table_constraints AS tc
          JOIN information_schema.constraint_column_usage AS ccu ON tc.constraint_name = ccu.constraint_name AND tc.table_schema = ccu.table_schema
          WHERE tc.constraint_type = 'UNIQUE'
      ) AS uq ON c.table_schema = uq.table_schema AND c.table_name = uq.table_name AND c.column_name = uq.column_name
      WHERE c.table_schema = 'public' AND c.table_name != 'SequelizeMeta'
      ORDER BY c.table_name, c.ordinal_position;
    `);

    const allSchemas = resSchemas.rows.reduce((acc, col) => {
      if (!acc[col.table_name]) {
        acc[col.table_name] = {};
      }

      const columnDefinition = {
        type: `%%${pgTypeToSequelize(col.data_type, col.udt_name)}%%`,
      };

      if (col.is_primary_key) columnDefinition.primaryKey = true;
      if (col.is_nullable === 'NO') columnDefinition.allowNull = false;
      // Note: This only captures single-column unique constraints.
      // Multi-column unique constraints are handled by the index generation below.
      if (col.is_unique) columnDefinition.unique = true;

      if (col.column_default !== null) {
        if (col.column_default.startsWith('nextval(')) {
          columnDefinition.autoIncrement = true;
        } else if (col.column_default.toLowerCase() === 'now()') {
          columnDefinition.defaultValue = "%%Sequelize.fn('NOW')%%";
        } else {
          const parsedDefault = parseDefaultValue(col.column_default, col.udt_name);
          if (parsedDefault !== undefined) {
            columnDefinition.defaultValue = parsedDefault;
          }
        }
      }

      acc[col.table_name][col.column_name] = columnDefinition;
      return acc;
    }, {});

    // --- Step 2: Gather all Indexes ---
    console.log('Gathering indexes...');
    const resIndexes = await client.query(`
      SELECT
        tablename,
        indexname,
        indexdef
      FROM
        pg_indexes
      WHERE
        schemaname = 'public'
        AND tablename != 'SequelizeMeta'
        -- Exclude primary key indexes, as Sequelize handles them via "primaryKey: true"
        AND indexname NOT IN (
            SELECT con.conname
            FROM pg_constraint con
            JOIN pg_class t ON con.conrelid = t.oid
            JOIN pg_namespace n ON t.relnamespace = n.oid
            WHERE n.nspname = 'public' AND con.contype = 'p'
        );
    `);

    const allIndexes = resIndexes.rows.reduce((acc, idx) => {
      const { tablename, indexname, indexdef } = idx;

      if (!acc[tablename]) {
        acc[tablename] = [];
      }

      // Regex to extract columns from the index definition
      // e.g., from "CREATE UNIQUE INDEX my_index ON public.my_table USING btree (col1, col2)"
      const columnMatch = indexdef.match(/\((.*)\)/);
      if (!columnMatch) {
        return acc; // Skip if we can't parse the columns
      }

      const columns = columnMatch[1].split(',').map(c => c.trim().replace(/"/g, ''));
      const isUnique = indexdef.includes('UNIQUE INDEX');

      const indexDefinition = {
        name: indexname,
        fields: columns,
      };

      if (isUnique) {
        indexDefinition.unique = true;
      }

      acc[tablename].push(indexDefinition);
      return acc;
    }, {});

    // --- Step 3: Combine and write to file ---
    const finalOutput = {
      allSchemas,
      allIndexes,
    };

    let outputString = JSON.stringify(finalOutput, null, 2);
    // Replace placeholder quotes with actual code
    outputString = outputString.replace(/"%%(.*?)%%"/g, '$1');

    const fileContent = `/* eslint-disable prettier/prettier */
// This file is auto-generated by the generate-schema.js script.
// Do not edit this file directly.
'use strict';
const { DataTypes, Sequelize } = require('sequelize');

module.exports = ${outputString};
`;

    fs.writeFileSync(outputFile, fileContent);
    console.log(`✅ Schema and index snapshot successfully written to ${outputFile}`);
  } catch (err) {
    console.error('❌ Error generating schema snapshot:', err);
    process.exit(1);
  } finally {
    await client.end();
  }
}

generateSchema();
