const AppService = require('../AppService');
const { InternalJobProfileData } = require('../../models');
const { UserAssessmentResult } = require('../../models');

class GenerateJobDescService extends AppService {
  constructor({ onetService, genAI, qdrantClient }) {
    super();
    this.onetService = onetService;
    this.genAI = genAI;
    this.qdrantClient = qdrantClient;
  }

  /**
   * Generate job description based on job title name
   * @param {string} name - Job title name
   * @param {Array} topUserIds - Array of top user ids
   * @returns {Promise<Object>} Job description object
   */
  async generateJobDesc(name = '', topUserIds = []) {
    const relatedJobTitles = await this.getRelatedJobTitles(name);
    const allJobTitles = [name, ...relatedJobTitles];

    const vectorizedJobTitles = await this.generateVectorTitles(allJobTitles);

    const [onetResults, internalResults] = await Promise.all([
      this.qdrantSearchOnet(vectorizedJobTitles),
      this.qdrantSearchInternal(vectorizedJobTitles),
    ]);

    const searchResults = {
      onetResults,
      internalResults,
    };

    const onetCodes = searchResults.onetResults.map(item => item.onetsoc_code);

    const contextData = await this.processContextData(searchResults, topUserIds);
    const jobDescription = await this.generateJobDescriptionWithAI(name, contextData);

    return {
      jobTitle: name,
      jobDescription,
      onetsocCodes: onetCodes,
    };
  }

  /**
   * Get 3 related job titles using Gemini API
   * @param {string} jobTitle - Original job title
   * @returns {Array} Array of 3 related job titles
   */
  async getRelatedJobTitles(jobTitle) {
    const systemPrompt = `
        You are an expert HR professional. Given a job title, 
        provide 3 closely related job titles that share similar skills, responsibilities, or career paths. 
        Return only a JSON array of strings with exactly 3 job titles.
    `;

    const userPrompt = `Job Title: ${jobTitle}\n\nProvide 3 closely related job titles:`;

    try {
      const response = await this.genAI.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: [{ role: 'user', parts: [{ text: userPrompt }] }],
        config: {
          temperature: 0,
          responseMimeType: 'application/json',
          systemInstruction: [{ text: systemPrompt }],
        },
      });

      const relatedTitles = JSON.parse(response.candidates[0].content.parts[0].text);
      return relatedTitles.slice(0, 3);
    } catch (error) {
      console.error('Error getting related job titles:', error);
      // Fallback to mock data
      return ['Senior Software Engineer', 'Full Stack Developer'];
    }
  }

  /**
   * Generate vector embeddings for job titles
   * @param {Array} jobTitles - Array of job titles
   * @returns {Array} Array of objects with jobTitle and vector
   */
  async generateVectorTitles(jobTitles) {
    const responses = await this.genAI.models.embedContent({
      model: 'gemini-embedding-001',
      contents: jobTitles,
    });

    const results = responses.embeddings.map((embedding, index) => ({
      jobTitle: jobTitles[index],
      vector: embedding.values,
    }));

    return results;
  }

  /**
   * Search Qdrant for ONET collection data
   * @param {Array} jobTitles - Job title to search
   * @returns {Array} Array of ONET results with onetsoc_code
   */
  async qdrantSearchOnet(jobTitles) {
    const collectionName = 'onet_job_title';

    const searches = jobTitles.map(({ _, vector }) => ({
      vector,
      limit: 3,
    }));

    const searchResult = await this.qdrantClient.searchBatch(collectionName, {
      searches,
    });

    const searchOnetResults = searchResult.flatMap(item => item.flatMap(result => result.id));

    const onetData = await this.qdrantClient.retrieve(collectionName, {
      ids: searchOnetResults,
    });

    const retriveData = onetData.map(item => ({
      onetsoc_code: item.payload.onetsoc_code,
      title: item.payload.job_title,
    }));

    return retriveData;
  }

  /**
   * Search Qdrant for internal collection data
   * @param {Array} jobTitles - Job title to search
   * @returns {Array} Array of internal results with table_internal_id
   */
  async qdrantSearchInternal(jobTitles) {
    const collectionName = 'internal_job_title';

    const searches = jobTitles.map(({ _, vector }) => ({
      vector,
      limit: 3,
    }));

    const searchResult = await this.qdrantClient.searchBatch(collectionName, {
      searches,
    });

    const qdrant_ids = searchResult.flatMap(item => item.flatMap(result => result.id));

    const internalData = await this.qdrantClient.retrieve(collectionName, {
      ids: qdrant_ids,
    });

    const retriveData = internalData.map(item => ({
      table_internal_id: item.payload.table_internal_id,
      title: item.payload.job_title,
    }));

    return retriveData;
  }

  /**
   * Process context data from ONET search results
   * @param {Array} onetResults - Array of ONET search results
   * @returns {Object} Processed ONET context data
   */
  async onetContextData(onetResults) {
    const onetsocCodes = onetResults.map(result => result.onetsoc_code);

    if (onetsocCodes.length === 0) {
      return {
        onetData: 'No onet data available',
        source: 'onet',
      };
    }

    const resultOccupations = await this.onetService.getOccupations(onetsocCodes);
    const resultTasks = await this.onetService.getTasks(onetsocCodes);

    const occupations = Object.values(resultOccupations)
      .map(result => `Job Title: ${result.title}\nJob Description: ${result.description}\n`)
      .join('\n\n');

    const tasks = resultTasks
      .map(
        result =>
          `Task: ${result.task}\nTask Type: ${result.task_type}\n Importance: ${result.importance}`,
      )
      .join('\n\n');

    return {
      occupations,
      tasks,
      source: 'onet',
    };
  }

  /**
   * Process context data from internal search results
   * @param {Array} internalResults - Array of internal search results
   * @returns {Object} Processed internal context data
   */
  async internalContextData(internalResults) {
    const internalIds = internalResults.map(result => result.table_internal_id);

    if (internalIds.length === 0) {
      return {
        internalData: 'No internal data available',
        source: 'internal',
      };
    }

    const results = await InternalJobProfileData.findAll({ where: { id: internalIds } });

    const internalData = results
      .map(
        result =>
          `Job Title: ${result.position_name}\nJob Description: ${result.main_responsibilities}\n`,
      )
      .join('\n\n');

    return {
      internalData,
      source: 'internal',
    };
  }

  /**
   * Process context data from user assessment results
   * @param {Array} userIds - Array of user IDs
   * @returns {Object} Processed user assessment data
   */
  async userAssessmentData(userIds) {
    if (userIds.length === 0) {
      return {
        userAssessmentData: 'No user assessment data available',
        source: 'user_assessment',
      };
    }

    const userAssessmentResults = await UserAssessmentResult.findAll({
      where: {
        user_id: userIds,
      },
    });

    // group assesment data by user_ids and assessment
    const groupedAssessmentData = userAssessmentResults.reduce((acc, cur) => {
      if (!acc[cur.user_id]) {
        acc[cur.user_id] = {};
      }
      acc[cur.user_id][cur.assessment] = cur;
      return acc;
    }, {});

    // format user assessment data
    const userAssessmentData = Object.entries(groupedAssessmentData).map(
      ([userId, data]) =>
        `
      User ID: ${userId}
      ${Object.entries(data)
        .map(
          ([assessment, result]) =>
            `
        Assessment: ${assessment}
        Aspect Name: ${result.aspect_name}
        Value Type: ${result.value_type}
        Value: ${result.value}
        `,
        )
        .join('\n')}
      `,
    );

    return {
      userAssessmentData,
      source: 'user_assessment',
    };
  }

  /**
   * Process all context data from search results
   * @param {Array} searchResults - Combined search results
   * @param {Array} topUserIds - Array of top user IDs
   * @returns {Object} Processed context data
   */
  async processContextData(searchResults, topUserIds) {
    const [onetContext, internalContext, userAssessmentContext] = await Promise.all([
      this.onetContextData(searchResults.onetResults),
      this.internalContextData(searchResults.internalResults),
      this.userAssessmentData(topUserIds),
    ]);

    return {
      onetContext,
      internalContext,
      userAssessmentContext,
    };
  }

  /**
   * Generate job description using AI with context data
   * @param {string} jobTitle - Original job title
   * @param {Array} contextData - Processed context data
   * @returns {Object} Generated job description
   */
  async generateJobDescriptionWithAI(jobTitle, contextData) {
    const [systemPrompt, userPrompt] = await Promise.all([
      this.getSystemPrompt(jobTitle, contextData),
      this.getUserPrompt(jobTitle),
    ]);

    const response = await this.genAI.models.generateContent({
      model: 'gemini-2.5-pro',
      contents: [{ role: 'user', parts: [{ text: userPrompt }] }],
      config: {
        temperature: 0.4,
        responseMimeType: 'application/json',
        systemInstruction: [{ text: systemPrompt }],
      },
    });

    const jobDescription = JSON.parse(response.candidates[0].content.parts[0].text);
    return jobDescription;
  }

  /**
   * Get system prompt for job description generation
   * @param {string} jobTitle - Job title
   * @param {Array} contextData - Context data from searches
   * @returns {string} System prompt
   */
  async getSystemPrompt(jobTitle, contextData) {
    return Promise.resolve(`
      You are an expert on domain of 'TARGET_JOB_TITLE' tasked with creating a clear, comprehensive, and compelling job description for a new vacancy on your team. Your goal is to attract top talent by accurately representing the role and its responsibilities.

      **Objective:**
      Analyze the provided contextual data—including internal job profiles, O*Net data, and task lists—to synthesize and generate a detailed job description for the specified 'TARGET_JOB_TITLE'.

      **TASK:**
      1.  Carefully review all sections of the 'CONTEXTUAL_DATA'.
      2.  Analyze the 'TOP_EMPLOYEE_RELATED_DATA' to understand the unique needs, skills, and preferences of top employees in the field
      3.  Identify the core responsibilities, skills, and tasks most relevant to the 'TARGET_JOB_TITLE'.
      4.  Synthesize this information into a new, coherent list of job responsibilities. Do not simply copy-paste. Adapt, rephrase, and combine concepts to create high-quality, professional descriptions.
      5.  Translate any relevant non-English information into fluent, professional English.
      6.  Format your final output strictly according to the defined JSON schema.
      7.  Ensure the job description is clear, concise, and engaging.

      **CRITICAL RULES:**
      -   **JSON Output Only:** Your entire response must be a single, valid JSON object, with no introductory text or explanations.
      -   **Relevance is Key:** Every point in the job description must be directly relevant to the 'TARGET_JOB_TITLE'.
      -   **Language:** The output must be in English.
      -   **Quantity:** Generate a comprehensive list of 20 distinct responsibilities.
      -   **Style:** Use clear, action-oriented language (e.g., "Design and implement...", "Collaborate with...", "Optimize and maintain...").
      -   **Professionalism:** Ensure the language is inclusive, professional, and free of any offensive terms.

      **JSON OUTPUT SCHEMA:**
      {
          "job_decs": String[]
      }

      **EXAMPLE OUTPUT:**
      {
          "job_decs": [
              "Design, develop, and maintain scalable and robust server-side applications and APIs.",
              "Collaborate with front-end developers, product managers, and other engineering teams to deliver high-quality features.",
              "Write clean, efficient, and well-documented code while adhering to best practices and coding standards.",
              "Optimize application performance by identifying and resolving performance bottlenecks in databases and services.",
              "Develop and execute unit, integration, and automated tests to ensure software quality and reliability.",
              "Participate in code reviews to maintain code quality and share knowledge with the team.",
              "Troubleshoot, debug, and upgrade existing software systems to resolve issues and enhance functionality.",
              "Design and manage database schemas, and write efficient SQL and NoSQL queries.",
              "Implement and maintain CI/CD pipelines for automated deployments and continuous integration.",
              "Contribute to architectural decisions and help shape the technical direction of our backend systems."
          ]
      }
      ### **INPUT DATA**

      **1. TARGET_JOB_TITLE:**
      ${jobTitle}

      **2. CONTEXTUAL_DATA:**

      **2.1. Internal Company Job Profiles:**
      ${Array.isArray(contextData) ? contextData.map(item => item.internalContext).join('\n') : contextData.internalContext}

      **2.2. O*Net Job Descriptions:**
      ${Array.isArray(contextData) ? contextData.map(item => item.onetContext).join('\n') : contextData.onetContext}

      **2.3. O*Net Related Tasks:**
      Task contains: Task Description, Task Type, and Importance 1-100 (higher importance means more important)
      ${Array.isArray(contextData) ? contextData.map(item => item.tasks || '').join('\n') : contextData.tasks || ''}

      **3. Top Employee Related Data**
      ${Array.isArray(contextData) ? contextData.map(item => item.userAssessmentContext).join('\n') : contextData.userAssessmentContext}
    `);
  }

  /**
   * Generate user prompt with job title and context data
   * @param {string} jobTitle - Job title
   * @returns {string} User prompt
   */
  async getUserPrompt(jobTitle) {
    const userPrompt = `# Job Title\n${jobTitle}\n\n`;
    return Promise.resolve(userPrompt);
  }
}

module.exports = GenerateJobDescService;
