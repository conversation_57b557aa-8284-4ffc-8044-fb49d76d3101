const AppService = require('./AppService');
const { sequelize } = require('../models');

class OnetService extends AppService {
  async getOccupations(onetsocCodes) {
    const sql = `
      SET search_path TO onet;
      SELECT
        onetsoc_code,
        title,
        description
      FROM occupation_data
      WHERE onetsoc_code IN (:onetsoc_codes);
    `;

    const occupations = await sequelize.query(sql, {
      replacements: { onetsoc_codes: onetsocCodes },
      type: sequelize.QueryTypes.SELECT,
    });

    const mappedOccupations = {};
    occupations.forEach(occupation => {
      mappedOccupations[occupation.onetsoc_code] = {
        title: occupation.title,
        description: occupation.description,
      };
    });

    return mappedOccupations;
  }

  async getKnowledges(onetsocCodes) {
    const sql = `
      SET search_path TO onet;
      SELECT
        k_im.onetsoc_code,
        cmr.element_name AS knowledge,
        cmr.description,
        ROUND((k_im.data_value - sr_im.minimum) * 100.0 / (sr_im.maximum - sr_im.minimum)) AS importance,
        ROUND((k_lv.data_value - sr_lv.minimum) * 100.0 / (sr_lv.maximum - sr_lv.minimum)) AS level_required
      FROM knowledge AS k_im
      JOIN scales_reference AS sr_im ON k_im.scale_id = sr_im.scale_id AND sr_im.scale_name = 'Importance'
      JOIN knowledge AS k_lv ON k_im.onetsoc_code = k_lv.onetsoc_code AND k_im.element_id = k_lv.element_id
      JOIN scales_reference AS sr_lv ON k_lv.scale_id = sr_lv.scale_id AND sr_lv.scale_name = 'Level'
      JOIN content_model_reference AS cmr ON k_im.element_id = cmr.element_id
      WHERE k_im.onetsoc_code IN (:onetsoc_codes)
      ORDER BY importance DESC;
    `;

    const knowledges = await sequelize.query(sql, {
      replacements: { onetsoc_codes: onetsocCodes },
      type: sequelize.QueryTypes.SELECT,
    });

    return knowledges;
  }

  async getSkills(onetsocCodes) {
    const sql = `
      SET search_path TO onet;
      SELECT
        s.onetsoc_code,
        cmr.element_name AS skill,
        cmr.description,
        ROUND(((MAX(CASE WHEN s.scale_id = 'IM' THEN s.data_value END) - 1) / 4.0) * 100) AS importance,
        ROUND((MAX(CASE WHEN s.scale_id = 'LV' THEN s.data_value END) / 7.0) * 100) AS required_level
      FROM skills s
      JOIN content_model_reference AS cmr ON s.element_id = cmr.element_id
      WHERE s.onetsoc_code IN (:onetsoc_codes)
      GROUP BY cmr.element_name,
        cmr.description,
        s.onetsoc_code
      HAVING MAX(CASE WHEN s.scale_id = 'IM' THEN s.data_value END) IS NOT NULL
        AND MAX(CASE WHEN s.scale_id = 'LV' THEN s.data_value END) IS NOT NULL
      ORDER BY importance DESC, required_level DESC;
    `;

    const skills = await sequelize.query(sql, {
      replacements: { onetsoc_codes: onetsocCodes },
      type: sequelize.QueryTypes.SELECT,
    });

    return skills;
  }

  async getAbilities(onetsocCodes) {
    const sql = `
      SET search_path TO onet;
      SELECT
        a.onetsoc_code,
        cmr.element_name AS ability,
        cmr.description,
        ROUND(MAX(CASE WHEN a.scale_id = 'IM' THEN a.data_value ELSE NULL END) / 5.0 * 100, 2) AS importance,
        ROUND(MAX(CASE WHEN a.scale_id = 'LV' THEN a.data_value ELSE NULL END) / 7.0 * 100, 2) AS level_required
      FROM abilities AS a
      JOIN content_model_reference AS cmr ON a.element_id = cmr.element_id
      WHERE a.onetsoc_code IN (:onetsoc_codes)
      GROUP BY cmr.element_name,
        cmr.description,
        a.onetsoc_code
      ORDER BY importance DESC,
        level_required DESC;
    `;

    const abilities = await sequelize.query(sql, {
      replacements: { onetsoc_codes: onetsocCodes },
      type: sequelize.QueryTypes.SELECT,
    });

    return abilities;
  }

  async getInterests(onetsocCodes) {
    const sql = `
      SET search_path TO onet;
      SELECT
        i.onetsoc_code,
        cmr.element_name AS interest,
        cmr.description,
        ROUND(((i.data_value - sr.minimum) / (sr.maximum - sr.minimum)) * 100) AS occupational_interest
      FROM interests AS i
      JOIN content_model_reference AS cmr ON i.element_id = cmr.element_id
      JOIN scales_reference AS sr ON i.scale_id = sr.scale_id
      WHERE i.onetsoc_code IN (:onetsoc_codes)
        AND sr.scale_name = 'Occupational Interests'
      ORDER BY occupational_interest DESC;
    `;

    const interests = await sequelize.query(sql, {
      replacements: { onetsoc_codes: onetsocCodes },
      type: sequelize.QueryTypes.SELECT,
    });

    return interests;
  }

  async getWorkValues(onetsocCodes) {
    const sql = `
      SET search_path TO onet;
      SELECT
        wv.onetsoc_code,
        cmr.element_name AS work_value,
        cmr.description,
        ROUND(((wv.data_value - sc.minimum) / (sc.maximum - sc.minimum)) * 100, 2) AS extent
      FROM work_values wv
      JOIN content_model_reference AS cmr ON cmr.element_id = wv.element_id
      JOIN scales_reference AS sc ON sc.scale_id = wv.scale_id
      WHERE wv.onetsoc_code IN (:onetsoc_codes)
        AND sc.scale_name = 'Extent'
      ORDER BY extent DESC;
    `;

    const workValues = await sequelize.query(sql, {
      replacements: { onetsoc_codes: onetsocCodes },
      type: sequelize.QueryTypes.SELECT,
    });

    return workValues;
  }

  async getWorkStyles(onetsocCodes) {
    const sql = `
      SET search_path TO onet;
      SELECT
        ws.onetsoc_code,
        cmr.element_name AS work_style,
        cmr.description,
        ROUND(((ws.data_value - sc.minimum) / (sc.maximum - sc.minimum)) * 100, 2) AS importance
      FROM work_styles ws
      JOIN content_model_reference AS cmr ON cmr.element_id = ws.element_id
      JOIN scales_reference AS sc ON sc.scale_id = ws.scale_id
      WHERE ws.onetsoc_code IN (:onetsoc_codes)
        AND sc.scale_name = 'Importance'
      ORDER BY importance DESC;
    `;

    const workStyles = await sequelize.query(sql, {
      replacements: { onetsoc_codes: onetsocCodes },
      type: sequelize.QueryTypes.SELECT,
    });

    return workStyles;
  }

  async getTasks(onetsocCodes) {
    const sql = `
      SELECT
        ts.task,
        ts.task_type,
      ROUND(((tr.data_value - sc.minimum) / (sc.maximum - sc.minimum)) * 100, 2) AS importance
      FROM task_statements ts
      JOIN task_ratings tr ON tr.task_id = ts.task_id
      JOIN scales_reference sc ON sc.scale_id = tr.scale_id
      WHERE ts.onetsoc_code IN (:onetsoc_codes)
        AND sc.scale_name = 'Importance'
      ORDER BY importance DESC;
    `;

    const tasks = await sequelize.query(sql, {
      replacements: { onetsoc_codes: onetsocCodes },
      type: sequelize.QueryTypes.SELECT,
    });

    return tasks;
  }
}

module.exports = OnetService;
