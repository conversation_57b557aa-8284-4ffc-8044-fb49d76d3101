const ApiOutput = require('./ApiOutput');

class UserJobVacancyOutput extends ApiOutput {
  /**
   * Format the UserJobVacancy data for the API response.
   * @param {Object} item - A UserJobVacancy model instance with nested user and profile data (for array formatting)
   * @returns {Object} Formatted data.
   */
  format() {
    const record = this.data;

    // Safely access nested user and profile data
    const user = record.user || {};
    const userProfile = user.profile || {};
    const currentPosition = userProfile.current_position || {};

    return {
      id: record.id,
      user: {
        id: user.id,
        name: user.name,
        // The current_position is directly from the user_profiles table
        current_position: currentPosition,
      },
      competency_match: record.competency_match,
      skill_match: record.skill_match,
      status: record.status,
    };
  }
}

module.exports = UserJobVacancyOutput;
