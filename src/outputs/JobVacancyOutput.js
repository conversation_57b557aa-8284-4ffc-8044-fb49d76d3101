const ApiOutput = require('./ApiOutput');

class JobVacancyOutput extends ApiOutput {
  /**
   * Format a single job vacancy for output
   * @param {Object} item - Job vacancy item to format (optional, uses this.data if not provided)
   * @returns {Object} Formatted job vacancy data
   */
  format() {
    return {
      id: this.data.id,
      name: this.data.name,
      job_desc: this.data.job_desc,
      job_description: this.data.job_description,
      reference_users: this.referenceUsersOutput(),
      status: this.data.status,
    };
  }

  listFormat() {
    return {
      id: this.data.id,
      name: this.data.name,
      status: this.data.status,
      created_at: this.data.created_at,
      ujv_count: this.options.countsById[this.data.id],
    };
  }

  referenceUsersOutput() {
    if (!this.options.referenceUsers) return [];

    return this.options.referenceUsers.map(user => {
      return {
        id: user.id,
        name: user.name,
      };
    });
  }
}

module.exports = JobVacancyOutput;
