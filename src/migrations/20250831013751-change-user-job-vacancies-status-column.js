'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('user_job_vacancies', 'status', {
      type: Sequelize.STRING,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('user_job_vacancies', 'status', {
      type: Sequelize.ENUM('recommended', 'not_recommended'),
      allowNull: false,
      defaultValue: 'not_recommended',
    });
  },
};
