/* eslint-disable prettier/prettier */
// This file is auto-generated by the generate-schema.js script.
// Do not edit this file directly.
'use strict';
const { DataTypes, Sequelize } = require('sequelize');

module.exports = {
  allSchemas: {
    internal_job_profile_data: {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      job_division: {
        type: DataTypes.STRING,
      },
      job_group: {
        type: DataTypes.STRING,
      },
      position_name: {
        type: DataTypes.STRING,
      },
      job_classification: {
        type: DataTypes.STRING,
      },
      job_family: {
        type: DataTypes.STRING,
      },
      sub_job_family: {
        type: DataTypes.STRING,
      },
      main_responsibilities: {
        type: DataTypes.TEXT,
      },
      work_input: {
        type: DataTypes.TEXT,
      },
      work_output: {
        type: DataTypes.TEXT,
      },
      success_criteria: {
        type: DataTypes.TEXT,
      },
      requirement: {
        type: DataTypes.TEXT,
      },
      competency: {
        type: DataTypes.TEXT,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    job_group_variables: {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      keywords: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: false,
        defaultValue: [],
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
      },
      order_level: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
    },
    job_titles: {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      prefilled_details: {
        type: DataTypes.JSONB,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    job_vacancies: {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      department: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      job_grade: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      job_description: {
        type: DataTypes.TEXT,
      },
      competencies: {
        type: DataTypes.JSONB,
        defaultValue: [],
      },
      skills: {
        type: DataTypes.JSONB,
        defaultValue: [],
      },
      job_title_id: {
        type: DataTypes.INTEGER,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      job_desc: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: false,
        defaultValue: [],
      },
      ksao: {
        type: DataTypes.JSONB,
        allowNull: false,
        defaultValue: {
          skills: [],
          abilities: [],
          knowledges: [],
          other_characteristics: [],
        },
      },
      related_user_ids: {
        type: DataTypes.ARRAY(DataTypes.INTEGER),
      },
      related_onetsoc_codes: {
        type: DataTypes.ARRAY(DataTypes.STRING),
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: 'draft',
      },
    },
    job_variables: {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      job_group_variable_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      object_table: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      object_column: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      object_selector: {
        type: DataTypes.JSONB,
        allowNull: false,
      },
      constants: {
        type: DataTypes.JSONB,
        allowNull: false,
        defaultValue: {},
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
      },
    },
    user_assessment_results: {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.INTEGER,
      },
      assessment: {
        type: DataTypes.STRING,
      },
      aspect_name: {
        type: DataTypes.STRING,
      },
      value_type: {
        type: DataTypes.STRING,
      },
      value: {
        type: DataTypes.STRING,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    user_competencies_profilings: {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.INTEGER,
      },
      profiling_date: {
        type: DataTypes.DATEONLY,
      },
      assessors: {
        type: DataTypes.ARRAY(DataTypes.STRING),
      },
      profile_as: {
        type: DataTypes.STRING,
      },
      readiness: {
        type: DataTypes.STRING,
      },
      metadata: {
        type: DataTypes.JSONB,
      },
      createdAt: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
    },
    user_job_vacancies: {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      job_vacancy_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      competency_match: {
        type: DataTypes.DOUBLE,
        allowNull: false,
        defaultValue: 0,
      },
      skill_match: {
        type: DataTypes.DOUBLE,
        allowNull: false,
        defaultValue: 0,
      },
      status: {
        type: DataTypes.STRING,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      match_rate: {
        type: DataTypes.DOUBLE,
      },
    },
    user_job_variables: {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      job_variable_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      constant: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      match_score: {
        type: DataTypes.DOUBLE,
        allowNull: false,
        defaultValue: 0,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    user_performance_reviews: {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      user_position_id: {
        type: DataTypes.INTEGER,
      },
      review_type: {
        type: DataTypes.STRING,
      },
      review_result: {
        type: DataTypes.JSONB,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    user_positions: {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      role_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      department: {
        type: DataTypes.STRING,
      },
      job_grade: {
        type: DataTypes.STRING,
      },
      starts_at: {
        type: DataTypes.DATEONLY,
      },
      ends_at: {
        type: DataTypes.DATEONLY,
      },
      external_source_id: {
        type: DataTypes.STRING,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    user_profiles: {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        unique: true,
      },
      phone_number: {
        type: DataTypes.STRING,
      },
      location: {
        type: DataTypes.STRING,
      },
      manager: {
        type: DataTypes.STRING,
      },
      current_position: {
        type: DataTypes.JSONB,
      },
      years_experience: {
        type: DataTypes.INTEGER,
      },
      performance_rating: {
        type: DataTypes.DOUBLE,
      },
      last_promotion: {
        type: DataTypes.DATEONLY,
      },
      education: {
        type: DataTypes.STRING,
      },
      competencies: {
        type: DataTypes.ARRAY(DataTypes.STRING),
      },
      skills: {
        type: DataTypes.ARRAY(DataTypes.STRING),
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    user_vacancy_group_variables: {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      vacancy_group_variable_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      average_match_score: {
        type: DataTypes.DOUBLE,
        allowNull: false,
        defaultValue: 0,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    users: {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      password_digest: {
        type: DataTypes.STRING,
      },
      role: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: 'user',
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    vacancy_group_variables: {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      job_vacancy_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      job_group_variable_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      keyword_match_count: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      match_type: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: 'filter',
      },
      weight: {
        type: DataTypes.DOUBLE,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      filters: {
        type: DataTypes.JSONB,
        allowNull: false,
        defaultValue: {},
      },
    },
  },
  allIndexes: {
    user_profiles: [
      {
        name: 'user_profiles_user_id_key',
        fields: ['user_id'],
        unique: true,
      },
      {
        name: 'user_profiles_user_id',
        fields: ['user_id'],
      },
    ],
    user_positions: [
      {
        name: 'user_positions_user_id',
        fields: ['user_id'],
      },
    ],
    user_job_vacancies: [
      {
        name: 'user_job_vacancies_user_id',
        fields: ['user_id'],
      },
      {
        name: 'user_job_vacancies_job_vacancy_id',
        fields: ['job_vacancy_id'],
      },
      {
        name: 'user_job_vacancies_user_id_job_vacancy_id_unique_constraint',
        fields: ['user_id', 'job_vacancy_id'],
        unique: true,
      },
      {
        name: 'user_job_vacancies_job_vacancy_id_status',
        fields: ['job_vacancy_id', 'status'],
      },
    ],
    users: [
      {
        name: 'users_email_key',
        fields: ['email'],
        unique: true,
      },
    ],
    job_variables: [
      {
        name: 'job_variables_job_group_variable_id',
        fields: ['job_group_variable_id'],
      },
      {
        name: 'job_variables_object_table_column_selector_unique_constraint',
        fields: ['object_table', 'object_column', 'object_selector'],
        unique: true,
      },
    ],
    vacancy_group_variables: [
      {
        name: 'vacancy_group_variables_vacancy_id_jgv_id_unique_constraint',
        fields: ['job_vacancy_id', 'job_group_variable_id'],
        unique: true,
      },
    ],
    user_job_variables: [
      {
        name: 'user_job_variables_user_id',
        fields: ['user_id'],
      },
      {
        name: 'user_job_variables_job_variable_id',
        fields: ['job_variable_id'],
      },
      {
        name: 'user_job_variables_user_id_jv_id_constant_unique_constraint',
        fields: ['user_id', 'job_variable_id', 'constant'],
        unique: true,
      },
    ],
    job_titles: [
      {
        name: 'job_titles_name_key',
        fields: ['name'],
        unique: true,
      },
      {
        name: 'job_titles_name_unique_constraint',
        fields: ['name'],
        unique: true,
      },
    ],
    job_vacancies: [
      {
        name: 'job_vacancies_department_index',
        fields: ['department'],
      },
      {
        name: 'job_vacancies_job_grade_index',
        fields: ['job_grade'],
      },
      {
        name: 'job_vacancies_job_title_id_index',
        fields: ['job_title_id'],
      },
    ],
    internal_job_profile_data: [
      {
        name: 'internal_job_profile_data_position_name_index',
        fields: ['position_name'],
      },
    ],
    user_vacancy_group_variables: [
      {
        name: 'user_vacancy_group_variables_user_id_vgv_id_unique_constraint',
        fields: ['user_id', 'vacancy_group_variable_id'],
        unique: true,
      },
    ],
    user_competencies_profilings: [
      {
        name: 'user_competencies_profilings_user_id',
        fields: ['user_id'],
      },
    ],
  },
};
