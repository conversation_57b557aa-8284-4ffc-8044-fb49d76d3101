'use strict';
const { DataTypes } = require('sequelize');
const AppModel = require('./AppModel');

class JobVacancy extends AppModel {
  static associate(models) {
    this.belongsTo(models.JobTitle, {
      foreignKey: 'job_title_id',
      as: 'jobTitle',
    });
    this.hasMany(models.UserJobVacancy, {
      foreignKey: 'job_vacancy_id',
      as: 'userRecommendations',
    });
    this.hasMany(models.VacancyGroupVariable, {
      foreignKey: 'job_vacancy_id',
      as: 'vacancyGroupVariables',
    });
  }

  static schema() {
    return {
      status: {
        type: DataTypes.STRING,
        validate: {
          isIn: [
            [
              'draft',
              'active',
              'generating_jobdesc',
              'generating_job_variables',
              'calculating_match_scores',
            ],
          ],
        },
      },
    };
  }

  static options() {
    return {
      tableName: 'job_vacancies',
    };
  }
}

module.exports = JobVacancy;
